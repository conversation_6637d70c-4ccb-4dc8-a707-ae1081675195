<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>慧美文化传播有限公司 - 专业个人造型与服饰搭配</title>
    <meta name="description" content="慧美文化传播有限公司专注于个人造型设计、服饰搭配咨询，为客户提供专业的形象管理服务">
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- 自定义 Tailwind 配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8'
                        },
                        purple: {
                            500: '#8b5cf6',
                            600: '#7c3aed'
                        }
                    },
                    fontFamily: {
                        'sans': ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- 自定义样式 -->
    <style>
        /* 玻璃拟态效果 */
        .glass {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .glass-dark {
            background: rgba(0, 0, 0, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        /* 渐变背景 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .gradient-bg-2 {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .gradient-bg-3 {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .hover-lift {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .hover-lift:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }
        
        /* 隐藏滚动条 */
        html, body {
            overflow-x: hidden;
        }
        
        ::-webkit-scrollbar {
            width: 6px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }
        
        /* 响应式文字 */
        .text-responsive {
            font-size: clamp(1rem, 2.5vw, 1.25rem);
        }
        
        .heading-responsive {
            font-size: clamp(2rem, 5vw, 3.5rem);
        }
    </style>
</head>

<body class="min-h-screen text-white overflow-x-hidden">
    <!-- 背景 -->
    <div class="fixed inset-0 gradient-bg"></div>
    <div class="fixed inset-0 opacity-50" style="background-image: url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1920&h=1080&fit=crop&crop=center'); background-size: cover; background-position: center;"></div>
    
    <!-- 主要内容 -->
    <div class="relative z-10">
        <!-- 导航栏 -->
        <nav class="glass fixed top-0 w-full z-50 transition-all duration-300">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <!-- Logo -->
                    <div class="flex-shrink-0 flex items-center">
                        <img class="h-8 w-8 mr-3" src="https://unpkg.com/lucide-static@latest/icons/sparkles.svg" alt="Logo">
                        <span class="font-bold text-xl">慧美文化</span>
                    </div>
                    
                    <!-- 导航菜单 -->
                    <div class="hidden md:block">
                        <div class="ml-10 flex items-baseline space-x-8">
                            <a href="#home" class="hover:text-blue-200 transition-colors duration-200 px-3 py-2 rounded-md text-sm font-medium">首页</a>
                            <a href="#about" class="hover:text-blue-200 transition-colors duration-200 px-3 py-2 rounded-md text-sm font-medium">关于我们</a>
                            <a href="#services" class="hover:text-blue-200 transition-colors duration-200 px-3 py-2 rounded-md text-sm font-medium">服务项目</a>
                            <a href="#app" class="hover:text-blue-200 transition-colors duration-200 px-3 py-2 rounded-md text-sm font-medium">小程序</a>
                            <a href="#contact" class="hover:text-blue-200 transition-colors duration-200 px-3 py-2 rounded-md text-sm font-medium">联系我们</a>
                        </div>
                    </div>
                    
                    <!-- 移动端菜单按钮 -->
                    <div class="md:hidden">
                        <button type="button" class="glass rounded-md p-2 inline-flex items-center justify-center hover:bg-white hover:bg-opacity-20 focus:outline-none" onclick="toggleMobileMenu()">
                            <img class="h-6 w-6" src="https://unpkg.com/lucide-static@latest/icons/menu.svg" alt="Menu">
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 移动端菜单 -->
            <div id="mobile-menu" class="md:hidden hidden">
                <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 glass">
                    <a href="#home" class="hover:bg-white hover:bg-opacity-20 block px-3 py-2 rounded-md text-base font-medium">首页</a>
                    <a href="#about" class="hover:bg-white hover:bg-opacity-20 block px-3 py-2 rounded-md text-base font-medium">关于我们</a>
                    <a href="#services" class="hover:bg-white hover:bg-opacity-20 block px-3 py-2 rounded-md text-base font-medium">服务项目</a>
                    <a href="#app" class="hover:bg-white hover:bg-opacity-20 block px-3 py-2 rounded-md text-base font-medium">小程序</a>
                    <a href="#contact" class="hover:bg-white hover:bg-opacity-20 block px-3 py-2 rounded-md text-base font-medium">联系我们</a>
                </div>
            </div>
        </nav>

        <!-- Hero 区域 -->
        <section id="home" class="pt-16 min-h-screen flex items-center justify-center">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <div class="fade-in">
                    <h1 class="heading-responsive font-bold mb-8 leading-tight">
                        <span class="block">专业个人造型</span>
                        <span class="block text-blue-200">服饰搭配专家</span>
                    </h1>
                    <p class="text-responsive mb-12 max-w-3xl mx-auto leading-relaxed opacity-90">
                        慧美文化传播有限公司致力于为每一位客户提供专业的个人形象设计服务，
                        通过科学的色彩分析和专业的搭配理念，让美成为您生活的日常。
                    </p>
                    <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                        <a href="#services" class="glass hover-lift px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 inline-flex items-center">
                            <img class="w-5 h-5 mr-2" src="https://unpkg.com/lucide-static@latest/icons/arrow-right.svg" alt="">
                            了解服务
                        </a>
                        <a href="#app" class="glass hover-lift px-8 py-4 rounded-full font-semibold text-lg transition-all duration-300 inline-flex items-center border-2 border-white border-opacity-30">
                            <img class="w-5 h-5 mr-2" src="https://unpkg.com/lucide-static@latest/icons/smartphone.svg" alt="">
                            体验小程序
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 关于我们 -->
        <section id="about" class="py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold mb-6">关于慧美文化</h2>
                    <p class="text-xl opacity-90 max-w-3xl mx-auto">
                        专注于个人形象管理，让每个人都能发现并展现最美的自己
                    </p>
                </div>
                
                <div class="grid lg:grid-cols-2 gap-16 items-center">
                    <div class="space-y-8">
                        <div class="glass hover-lift rounded-3xl p-8">
                            <div class="flex items-center mb-6">
                                <img class="w-8 h-8 mr-4" src="https://unpkg.com/lucide-static@latest/icons/target.svg" alt="">
                                <h3 class="text-2xl font-bold">我们的使命</h3>
                            </div>
                            <p class="text-lg leading-relaxed opacity-90">
                                通过专业的形象设计和搭配服务，帮助每一位客户发现自己的独特魅力，
                                提升个人自信，在职场和生活中展现最佳状态。
                            </p>
                        </div>
                        
                        <div class="glass hover-lift rounded-3xl p-8">
                            <div class="flex items-center mb-6">
                                <img class="w-8 h-8 mr-4" src="https://unpkg.com/lucide-static@latest/icons/award.svg" alt="">
                                <h3 class="text-2xl font-bold">专业优势</h3>
                            </div>
                            <p class="text-lg leading-relaxed opacity-90">
                                拥有资深的形象设计团队，结合国际先进的色彩理论和搭配技巧，
                                为客户提供个性化、专业化的形象管理解决方案。
                            </p>
                        </div>
                    </div>
                    
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=800&fit=crop&crop=center" 
                             alt="专业形象设计" 
                             class="rounded-3xl shadow-2xl w-full h-96 object-cover hover-lift">
                        <div class="absolute inset-0 bg-gradient-to-t from-black from-0% via-transparent to-transparent rounded-3xl"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 服务项目 -->
        <section id="services" class="py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold mb-6">专业服务</h2>
                    <p class="text-xl opacity-90 max-w-3xl mx-auto">
                        全方位的个人形象管理服务，量身定制专属于您的美丽方案
                    </p>
                </div>
                
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- 服务卡片 1 -->
                    <div class="glass hover-lift rounded-3xl p-8 text-center">
                        <div class="gradient-bg w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6">
                            <img class="w-8 h-8" src="https://unpkg.com/lucide-static@latest/icons/palette.svg" alt="">
                        </div>
                        <h3 class="text-xl font-bold mb-4">个人色彩分析</h3>
                        <p class="opacity-90 leading-relaxed">
                            专业色彩顾问为您分析最适合的色彩搭配，
                            找到专属于您的色彩密码，让肌肤更加光彩照人。
                        </p>
                    </div>
                    
                    <!-- 服务卡片 2 -->
                    <div class="glass hover-lift rounded-3xl p-8 text-center">
                        <div class="gradient-bg-2 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6">
                            <img class="w-8 h-8" src="https://unpkg.com/lucide-static@latest/icons/shirt.svg" alt="">
                        </div>
                        <h3 class="text-xl font-bold mb-4">服饰风格定制</h3>
                        <p class="opacity-90 leading-relaxed">
                            根据个人特质、职业需求和生活方式，
                            为您量身定制专属的服饰风格和搭配方案。
                        </p>
                    </div>
                    
                    <!-- 服务卡片 3 -->
                    <div class="glass hover-lift rounded-3xl p-8 text-center">
                        <div class="gradient-bg-3 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6">
                            <img class="w-8 h-8" src="https://unpkg.com/lucide-static@latest/icons/user-check.svg" alt="">
                        </div>
                        <h3 class="text-xl font-bold mb-4">整体形象设计</h3>
                        <p class="opacity-90 leading-relaxed">
                            从发型设计到妆容搭配，从服装选择到配饰点缀，
                            全方位打造您的完美形象。
                        </p>
                    </div>
                    
                    <!-- 服务卡片 4 -->
                    <div class="glass hover-lift rounded-3xl p-8 text-center">
                        <div class="gradient-bg w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6">
                            <img class="w-8 h-8" src="https://unpkg.com/lucide-static@latest/icons/briefcase.svg" alt="">
                        </div>
                        <h3 class="text-xl font-bold mb-4">职场形象顾问</h3>
                        <p class="opacity-90 leading-relaxed">
                            专业的职场形象指导，帮助您在商务场合中
                            展现专业素养和个人魅力。
                        </p>
                    </div>
                    
                    <!-- 服务卡片 5 -->
                    <div class="glass hover-lift rounded-3xl p-8 text-center">
                        <div class="gradient-bg-2 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6">
                            <img class="w-8 h-8" src="https://unpkg.com/lucide-static@latest/icons/shopping-bag.svg" alt="">
                        </div>
                        <h3 class="text-xl font-bold mb-4">购物陪同服务</h3>
                        <p class="opacity-90 leading-relaxed">
                            专业顾问陪同购物，精准挑选适合您的服饰单品，
                            避免盲目消费，建立高质量衣橱。
                        </p>
                    </div>
                    
                    <!-- 服务卡片 6 -->
                    <div class="glass hover-lift rounded-3xl p-8 text-center">
                        <div class="gradient-bg-3 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6">
                            <img class="w-8 h-8" src="https://unpkg.com/lucide-static@latest/icons/heart.svg" alt="">
                        </div>
                        <h3 class="text-xl font-bold mb-4">衣橱整理优化</h3>
                        <p class="opacity-90 leading-relaxed">
                            梳理现有衣橱，优化搭配组合，
                            让您的每一件衣服都能发挥最大价值。
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 小程序展示 -->
        <section id="app" class="py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold mb-6">智能穿搭小程序</h2>
                    <p class="text-xl opacity-90 max-w-3xl mx-auto">
                        随时随地享受专业搭配服务，AI智能推荐让美丽触手可及
                    </p>
                </div>
                
                <div class="grid lg:grid-cols-2 gap-16 items-center">
                    <div class="space-y-8">
                        <div class="glass rounded-3xl p-8">
                            <h3 class="text-2xl font-bold mb-6">小程序特色功能</h3>
                            <div class="space-y-6">
                                <div class="flex items-start">
                                    <img class="w-6 h-6 mr-4 mt-1" src="https://unpkg.com/lucide-static@latest/icons/camera.svg" alt="">
                                    <div>
                                        <h4 class="font-semibold mb-2">AI智能识别</h4>
                                        <p class="opacity-90">拍照上传服装，AI自动识别并分类管理您的衣橱</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <img class="w-6 h-6 mr-4 mt-1" src="https://unpkg.com/lucide-static@latest/icons/sparkles.svg" alt="">
                                    <div>
                                        <h4 class="font-semibold mb-2">个性化推荐</h4>
                                        <p class="opacity-90">基于个人特征和喜好，推荐最适合的搭配方案</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <img class="w-6 h-6 mr-4 mt-1" src="https://unpkg.com/lucide-static@latest/icons/cloud.svg" alt="">
                                    <div>
                                        <h4 class="font-semibold mb-2">天气感知</h4>
                                        <p class="opacity-90">结合天气信息，智能推荐适合当日的穿搭组合</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start">
                                    <img class="w-6 h-6 mr-4 mt-1" src="https://unpkg.com/lucide-static@latest/icons/users.svg" alt="">
                                    <div>
                                        <h4 class="font-semibold mb-2">社交分享</h4>
                                        <p class="opacity-90">与好友分享搭配心得，获得专业意见和点赞支持</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <div class="glass rounded-2xl p-6 inline-block">
                                <p class="text-lg font-semibold mb-4">扫码体验小程序</p>
                                <div class="w-32 h-32 bg-white bg-opacity-20 rounded-xl mx-auto flex items-center justify-center">
                                    <img class="w-20 h-20" src="https://unpkg.com/lucide-static@latest/icons/qr-code.svg" alt="二维码">
                                </div>
                                <p class="text-sm opacity-75 mt-4">微信扫码立即使用</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-6">
                        <!-- 小程序截图占位 -->
                        <div class="glass rounded-2xl p-4 hover-lift">
                            <div class="bg-white bg-opacity-20 rounded-xl h-80 flex items-center justify-center">
                                <div class="text-center">
                                    <img class="w-12 h-12 mx-auto mb-3" src="https://unpkg.com/lucide-static@latest/icons/image.svg" alt="">
                                    <p class="text-sm opacity-75">小程序截图 1</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="glass rounded-2xl p-4 hover-lift">
                            <div class="bg-white bg-opacity-20 rounded-xl h-80 flex items-center justify-center">
                                <div class="text-center">
                                    <img class="w-12 h-12 mx-auto mb-3" src="https://unpkg.com/lucide-static@latest/icons/image.svg" alt="">
                                    <p class="text-sm opacity-75">小程序截图 2</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="glass rounded-2xl p-4 hover-lift">
                            <div class="bg-white bg-opacity-20 rounded-xl h-80 flex items-center justify-center">
                                <div class="text-center">
                                    <img class="w-12 h-12 mx-auto mb-3" src="https://unpkg.com/lucide-static@latest/icons/image.svg" alt="">
                                    <p class="text-sm opacity-75">小程序截图 3</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="glass rounded-2xl p-4 hover-lift">
                            <div class="bg-white bg-opacity-20 rounded-xl h-80 flex items-center justify-center">
                                <div class="text-center">
                                    <img class="w-12 h-12 mx-auto mb-3" src="https://unpkg.com/lucide-static@latest/icons/image.svg" alt="">
                                    <p class="text-sm opacity-75">小程序截图 4</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 团队介绍 -->
        <section class="py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold mb-6">专业团队</h2>
                    <p class="text-xl opacity-90 max-w-3xl mx-auto">
                        汇聚行业精英，用专业和热情为您提供最优质的服务
                    </p>
                </div>
                
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- 团队成员 1 -->
                    <div class="glass hover-lift rounded-3xl p-8 text-center">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b692?w=200&h=200&fit=crop&crop=face" 
                             alt="首席设计师" 
                             class="w-24 h-24 rounded-full mx-auto mb-6 object-cover">
                        <h3 class="text-xl font-bold mb-2">张慧美</h3>
                        <p class="text-blue-200 mb-4">首席形象设计师</p>
                        <p class="opacity-90 text-sm leading-relaxed">
                            10年形象设计经验，国际色彩分析师认证，
                            擅长为客户打造专属个人风格。
                        </p>
                    </div>
                    
                    <!-- 团队成员 2 -->
                    <div class="glass hover-lift rounded-3xl p-8 text-center">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop&crop=face" 
                             alt="资深顾问" 
                             class="w-24 h-24 rounded-full mx-auto mb-6 object-cover">
                        <h3 class="text-xl font-bold mb-2">李文雅</h3>
                        <p class="text-blue-200 mb-4">资深形象顾问</p>
                        <p class="opacity-90 text-sm leading-relaxed">
                            专注职场形象管理，曾为多位企业高管
                            提供专业形象咨询服务。
                        </p>
                    </div>
                    
                    <!-- 团队成员 3 -->
                    <div class="glass hover-lift rounded-3xl p-8 text-center">
                        <img src="https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=200&h=200&fit=crop&crop=face" 
                             alt="搭配师" 
                             class="w-24 h-24 rounded-full mx-auto mb-6 object-cover">
                        <h3 class="text-xl font-bold mb-2">王雅琪</h3>
                        <p class="text-blue-200 mb-4">高级搭配师</p>
                        <p class="opacity-90 text-sm leading-relaxed">
                            时尚搭配专家，对流行趋势敏感度极高，
                            善于将时尚元素融入日常生活。
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系我们 -->
        <section id="contact" class="py-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold mb-6">联系我们</h2>
                    <p class="text-xl opacity-90 max-w-3xl mx-auto">
                        期待与您的合作，让我们一起开启美丽新篇章
                    </p>
                </div>
                
                <div class="grid lg:grid-cols-2 gap-16 items-center">
                    <div class="space-y-8">
                        <div class="glass rounded-3xl p-8">
                            <h3 class="text-2xl font-bold mb-8">联系方式</h3>
                            <div class="space-y-6">
                                <div class="flex items-center">
                                    <div class="gradient-bg w-12 h-12 rounded-xl flex items-center justify-center mr-4">
                                        <img class="w-6 h-6" src="https://unpkg.com/lucide-static@latest/icons/phone.svg" alt="">
                                    </div>
                                    <div>
                                        <p class="font-semibold">联系电话</p>
                                        <p class="opacity-90">************</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-center">
                                    <div class="gradient-bg-2 w-12 h-12 rounded-xl flex items-center justify-center mr-4">
                                        <img class="w-6 h-6" src="https://unpkg.com/lucide-static@latest/icons/mail.svg" alt="">
                                    </div>
                                    <div>
                                        <p class="font-semibold">邮箱地址</p>
                                        <p class="opacity-90"><EMAIL></p>
                                    </div>
                                </div>
                                
                                <div class="flex items-center">
                                    <div class="gradient-bg-3 w-12 h-12 rounded-xl flex items-center justify-center mr-4">
                                        <img class="w-6 h-6" src="https://unpkg.com/lucide-static@latest/icons/map-pin.svg" alt="">
                                    </div>
                                    <div>
                                        <p class="font-semibold">公司地址</p>
                                        <p class="opacity-90">北京市朝阳区时尚大厦18层</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-center">
                                    <div class="gradient-bg w-12 h-12 rounded-xl flex items-center justify-center mr-4">
                                        <img class="w-6 h-6" src="https://unpkg.com/lucide-static@latest/icons/clock.svg" alt="">
                                    </div>
                                    <div>
                                        <p class="font-semibold">营业时间</p>
                                        <p class="opacity-90">周一至周日 9:00-18:00</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="glass rounded-3xl p-8">
                            <h3 class="text-2xl font-bold mb-6">关注我们</h3>
                            <div class="flex space-x-4">
                                <a href="#" class="gradient-bg w-12 h-12 rounded-xl flex items-center justify-center hover-lift">
                                    <img class="w-6 h-6" src="https://unpkg.com/lucide-static@latest/icons/message-circle.svg" alt="微信">
                                </a>
                                <a href="#" class="gradient-bg-2 w-12 h-12 rounded-xl flex items-center justify-center hover-lift">
                                    <img class="w-6 h-6" src="https://unpkg.com/lucide-static@latest/icons/share-2.svg" alt="微博">
                                </a>
                                <a href="#" class="gradient-bg-3 w-12 h-12 rounded-xl flex items-center justify-center hover-lift">
                                    <img class="w-6 h-6" src="https://unpkg.com/lucide-static@latest/icons/instagram.svg" alt="小红书">
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop&crop=center" 
                             alt="联系我们" 
                             class="rounded-3xl shadow-2xl w-full h-96 object-cover hover-lift">
                        <div class="absolute inset-0 bg-gradient-to-t from-black from-0% via-transparent to-transparent rounded-3xl"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="glass-dark py-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid md:grid-cols-4 gap-8">
                    <div class="col-span-2">
                        <div class="flex items-center mb-6">
                            <img class="h-8 w-8 mr-3" src="https://unpkg.com/lucide-static@latest/icons/sparkles.svg" alt="Logo">
                            <span class="font-bold text-xl">慧美文化传播有限公司</span>
                        </div>
                        <p class="opacity-90 leading-relaxed mb-6">
                            专业的个人形象管理服务商，致力于让每个人都能展现最美的自己。
                            通过科学的分析和专业的指导，为您打造独特的个人魅力。
                        </p>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold mb-4">服务项目</h4>
                        <ul class="space-y-2 opacity-90">
                            <li><a href="#" class="hover:text-blue-200 transition-colors">个人色彩分析</a></li>
                            <li><a href="#" class="hover:text-blue-200 transition-colors">服饰风格定制</a></li>
                            <li><a href="#" class="hover:text-blue-200 transition-colors">整体形象设计</a></li>
                            <li><a href="#" class="hover:text-blue-200 transition-colors">职场形象顾问</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold mb-4">快速链接</h4>
                        <ul class="space-y-2 opacity-90">
                            <li><a href="#about" class="hover:text-blue-200 transition-colors">关于我们</a></li>
                            <li><a href="#services" class="hover:text-blue-200 transition-colors">服务项目</a></li>
                            <li><a href="#app" class="hover:text-blue-200 transition-colors">小程序</a></li>
                            <li><a href="#contact" class="hover:text-blue-200 transition-colors">联系我们</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="border-t border-white border-opacity-20 mt-12 pt-8 text-center opacity-75">
                    <p>&copy; 2024 慧美文化传播有限公司. 保留所有权利.</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script>
        // 移动端菜单切换
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }
        
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    // 关闭移动端菜单
                    document.getElementById('mobile-menu').classList.add('hidden');
                }
            });
        });
        
        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const nav = document.querySelector('nav');
            if (window.scrollY > 50) {
                nav.style.background = 'rgba(255, 255, 255, 0.1)';
                nav.style.backdropFilter = 'blur(20px)';
            } else {
                nav.style.background = 'rgba(255, 255, 255, 0.25)';
                nav.style.backdropFilter = 'blur(10px)';
            }
        });
        
        // 添加滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);
        
        // 观察所有需要动画的元素
        document.querySelectorAll('.glass, .hover-lift').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    </script>
</body>
</html> 