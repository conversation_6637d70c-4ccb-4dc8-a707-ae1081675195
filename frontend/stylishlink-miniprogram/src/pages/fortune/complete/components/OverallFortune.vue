<script setup lang="ts">
import type { OverallFortune } from '@/api/fortune'
import { computed, getCurrentInstance, inject, nextTick, onMounted, ref, watch } from 'vue'

interface Props {
  fortuneData: OverallFortune
}

const props = defineProps<Props>()

// 图表配置 - iOS真机兼容性优化
const chartConfig = {
  width: 900, // 减少宽度以适配iOS真机限制（原1100px可能过大）
  height: 200,
  paddingTop: 30,
  paddingBottom: 50,
  paddingLeft: 40,
  paddingRight: 40,
}

// 计算图表内部尺寸
const chartInner = computed(() => ({
  width: chartConfig.width - chartConfig.paddingLeft - chartConfig.paddingRight,
  height: chartConfig.height - chartConfig.paddingTop - chartConfig.paddingBottom,
}))

const scrollContainer = ref(null)
// iOS真机兼容的Canvas ID格式
const canvasId = `fortuneCanvas${Date.now()}`
const instance = getCurrentInstance()
const canvasReady = ref(false)
let retryCount = 0
const maxRetry = 3

// Canvas绘制状态管理
const isDrawing = ref(false)
const isScrolling = ref(false) // 🚀 滚动状态管理

// 🚀 获取全局滚动状态
const isPageScrolling = inject<any>('isPageScrolling', ref(false))

// 当前查看的十年（用于滚动联动）- 添加安全检查
const currentViewingDecade = ref(props.fortuneData?.currentDecade || 1)

// 当前查看的十年数据 - 添加安全检查
const currentDecadeData = computed(() => {
  if (!props.fortuneData?.decadeFortunes || props.fortuneData.decadeFortunes.length === 0) {
    return {
      decade: 1,
      ageRange: '0-10岁',
      yearRange: '未知',
      theme: '暂无数据',
      score: 0,
      description: '数据加载中...',
      keyEvents: [],
    }
  }
  return props.fortuneData.decadeFortunes.find(
    decade => decade.decade === currentViewingDecade.value,
  ) || props.fortuneData.decadeFortunes[0]
})

// 滚动监听处理 - iOS性能优化版本
let scrollTimer: number | null = null
let lastScrollLeft = 0

function handleScroll(event: any) {
  const scrollLeft = event.detail.scrollLeft

  // 🚀 新方案：轻量级滚动状态管理
  if (!isScrolling.value) {
    isScrolling.value = true
  }

  // 🚀 提高滚动阈值，减少无效处理
  if (Math.abs(scrollLeft - lastScrollLeft) < 8) {
    return
  }

  lastScrollLeft = scrollLeft

  // 清除之前的滚动计时器
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }

  // 🚀 优化防抖：适中的延迟，平衡性能和响应性
  scrollTimer = setTimeout(() => {
    // 安全检查：确保数据存在
    if (!props.fortuneData?.decadeFortunes || props.fortuneData.decadeFortunes.length === 0) {
      isScrolling.value = false
      scrollTimer = null
      return
    }

    const containerWidth = chartInner.value.width
    const decadeCount = props.fortuneData.decadeFortunes.length

    // 重新计算：基于滚动位置和容器宽度的精确映射
    const scrollRatio = scrollLeft / Math.max(containerWidth - 200, 1)
    const decadeIndex = Math.round(scrollRatio * (decadeCount - 1))
    const clampedIndex = Math.max(0, Math.min(decadeIndex, decadeCount - 1))

    const newDecade = props.fortuneData.decadeFortunes[clampedIndex]?.decade || 1

    // 只有当十年真正改变时才更新
    if (newDecade !== currentViewingDecade.value) {
      currentViewingDecade.value = newDecade
    }

    // 🚀 滚动处理完成，恢复正常状态
    isScrolling.value = false
    scrollTimer = null
  }, 200) // 🚀 平衡的防抖延迟：200ms
}

// 生成X轴刻度（每个十年）
const xAxisTicks = computed(() => {
  const ticks = []
  const decadeCount = props.fortuneData.decadeFortunes.length

  for (let i = 0; i < decadeCount; i++) {
    const x = chartConfig.paddingLeft + (i / Math.max(decadeCount - 1, 1)) * chartInner.value.width
    const decade = props.fortuneData.decadeFortunes[i]
    ticks.push({
      x,
      decade: decade.decade,
      ageRange: decade.ageRange,
      yearRange: decade.yearRange,
    })
  }
  return ticks
})

// 生成Y轴刻度（每20分）
const yAxisTicks = computed(() => {
  const ticks = []
  for (let i = 0; i <= 100; i += 20) {
    const y = chartConfig.paddingTop + chartInner.value.height - (i / 100) * chartInner.value.height
    ticks.push({ y, score: i })
  }
  return ticks
})

// Canvas初始化检查
function checkCanvasReady() {
  try {
    console.warn('检查Canvas就绪状态 - ID:', canvasId)
    const ctx = uni.createCanvasContext(canvasId, instance)
    if (ctx) {
      console.warn('Canvas上下文创建成功')
      canvasReady.value = true
      return true
    }
    else {
      console.warn('Canvas上下文为空')
    }
  }
  catch (error) {
    console.error('Canvas上下文创建失败:', error)
  }
  return false
}

// Canvas错误处理
function handleCanvasError(error: any) {
  console.error('Canvas元素错误:', error)
  console.warn('Canvas配置信息:', {
    canvasId,
    width: chartConfig.width,
    height: chartConfig.height,
    isReady: canvasReady.value,
  })
}

// 绘制图表主函数
function drawChart() {
  console.warn('OverallFortune: 开始绘制运势图检查', {
    canvasId,
    width: chartConfig.width,
    height: chartConfig.height,
    hasFortuneData: !!props.fortuneData,
    decadeCount: props.fortuneData?.decadeFortunes?.length || 0,
    currentDecade: props.fortuneData?.currentDecade,
    isDrawing: isDrawing.value,
    canvasReady: canvasReady.value,
  })

  if (isDrawing.value) {
    console.warn('正在绘制中，跳过重复绘制')
    return // 防止重复绘制
  }

  // 🚀 优化滚动检测：只在已经绘制过且正在滚动时跳过重绘
  if (isDrawing.value) {
    console.warn('OverallFortune: 正在绘制中，跳过重绘')
    return
  }

  // 🚀 只有在Canvas已经初始化且正在滚动时才跳过重绘
  if (canvasReady.value && (isScrolling.value || isPageScrolling.value)) {
    console.warn('OverallFortune: 滚动中，跳过重绘', {
      localScrolling: isScrolling.value,
      pageScrolling: isPageScrolling.value,
    })
    return
  }

  if (!canvasReady.value && !checkCanvasReady()) {
    console.warn('Canvas未就绪，重试中...', `第${retryCount + 1}次尝试`)
    if (retryCount < maxRetry) {
      retryCount++
      setTimeout(() => {
        drawChart()
      }, 1000 * retryCount)
    }
    else {
      console.error('Canvas初始化失败，已达到最大重试次数')
    }
    return
  }

  isDrawing.value = true
  console.warn('Canvas就绪，开始绘制...')

  const ctx = uni.createCanvasContext(canvasId, instance)
  if (!ctx) {
    console.error('获取Canvas上下文失败 - canvasId:', canvasId)
    isDrawing.value = false
    return
  }

  try {
    // 清空画布
    ctx.clearRect(0, 0, chartConfig.width, chartConfig.height)

    // 绘制背景
    ctx.setFillStyle('transparent')
    ctx.fillRect(0, 0, chartConfig.width, chartConfig.height)

    // 绘制坐标轴
    drawAxis(ctx)

    // 绘制刻度标签
    drawLabels(ctx)

    // 绘制运势曲线
    drawFortuneLine(ctx)

    // 绘制关键节点
    drawKeyPoints(ctx)

    // 🚀 优化：只在绘制提交前检查滚动状态，不阻止初始绘制
    // 注释掉过于严格的滚动检查，避免阻止初始绘制
    // if (isScrolling.value || isPageScrolling.value) {
    //   console.warn('运势图绘制时检测到滚动，延迟提交')
    //   isDrawing.value = false
    //   return
    // }

    ctx.draw(false, () => {
      console.warn('运势图绘制完成')
      isDrawing.value = false

      // 🚀 绘制完成后，设置更长的稳定期，避免立即重绘
      setTimeout(() => {
        // 稳定期结束，允许下次绘制
      }, 200) // 增加稳定期到200ms
    })

    console.warn('运势图绘制指令发送完成')
  }
  catch (error) {
    console.error('Chart drawing error:', error)
  }
}

// 绘制坐标轴
function drawAxis(ctx: any) {
  // 调浅坐标轴颜色
  ctx.setStrokeStyle('rgba(0, 0, 0, 0.3)')
  ctx.setLineWidth(1)

  // X轴
  ctx.beginPath()
  ctx.moveTo(chartConfig.paddingLeft, chartConfig.height - chartConfig.paddingBottom)
  ctx.lineTo(chartConfig.width - chartConfig.paddingRight, chartConfig.height - chartConfig.paddingBottom)
  ctx.stroke()

  // Y轴
  ctx.beginPath()
  ctx.moveTo(chartConfig.paddingLeft, chartConfig.paddingTop)
  ctx.lineTo(chartConfig.paddingLeft, chartConfig.height - chartConfig.paddingBottom)
  ctx.stroke()
}

// 绘制标签
function drawLabels(ctx: any) {
  ctx.setFillStyle('rgba(0, 0, 0, 0.6)')
  ctx.setFontSize(10)
  ctx.setTextAlign('center')
  ctx.setTextBaseline('middle')

  // Y轴标签
  yAxisTicks.value.forEach((tick) => {
    ctx.setTextAlign('right')
    ctx.fillText(`${tick.score}`, chartConfig.paddingLeft - 8, tick.y)
  })

  // X轴标签 - 显示年龄范围
  ctx.setTextAlign('center')
  xAxisTicks.value.forEach((tick) => {
    const text = tick.ageRange
    ctx.fillText(text, tick.x, chartConfig.height - chartConfig.paddingBottom + 20)
  })
}

// 绘制运势曲线 - 添加安全检查和占位图表
function drawFortuneLine(ctx: any) {
  // 🚀 优化：如果没有数据，绘制占位图表
  if (!props.fortuneData?.decadeFortunes || props.fortuneData.decadeFortunes.length === 0) {
    console.warn('OverallFortune: 没有运势数据，绘制占位图表')
    drawPlaceholderChart(ctx)
    return
  }

  // 创建渐变
  const gradient = ctx.createLinearGradient(0, chartConfig.paddingTop, 0, chartConfig.height - chartConfig.paddingBottom)
  gradient.addColorStop(0, '#667eea')
  gradient.addColorStop(1, '#764ba2')

  ctx.setStrokeStyle(gradient)
  ctx.setLineWidth(3)
  ctx.setLineCap('round')

  ctx.beginPath()

  const decadeCount = props.fortuneData.decadeFortunes.length

  props.fortuneData.decadeFortunes.forEach((decade, index) => {
    const x = chartConfig.paddingLeft + (index / Math.max(decadeCount - 1, 1)) * chartInner.value.width
    const y = chartConfig.paddingTop + chartInner.value.height - (decade.score / 100) * chartInner.value.height

    if (index === 0) {
      ctx.moveTo(x, y)
    }
    else {
      ctx.lineTo(x, y)
    }
  })

  ctx.stroke()
}

// 🚀 新增：绘制占位图表
function drawPlaceholderChart(ctx: any) {
  // 绘制虚线占位曲线
  ctx.setStrokeStyle('rgba(102, 126, 234, 0.3)')
  ctx.setLineWidth(2)
  ctx.setLineDash([5, 5]) // 虚线样式

  ctx.beginPath()

  // 绘制一条平缓的占位曲线
  const points = [
    { x: chartConfig.paddingLeft, y: chartConfig.paddingTop + chartInner.value.height * 0.7 },
    { x: chartConfig.paddingLeft + chartInner.value.width * 0.3, y: chartConfig.paddingTop + chartInner.value.height * 0.5 },
    { x: chartConfig.paddingLeft + chartInner.value.width * 0.7, y: chartConfig.paddingTop + chartInner.value.height * 0.6 },
    { x: chartConfig.paddingLeft + chartInner.value.width, y: chartConfig.paddingTop + chartInner.value.height * 0.4 },
  ]

  points.forEach((point, index) => {
    if (index === 0) {
      ctx.moveTo(point.x, point.y)
    }
    else {
      ctx.lineTo(point.x, point.y)
    }
  })

  ctx.stroke()
  ctx.setLineDash([]) // 重置虚线样式

  // 绘制占位文字
  ctx.setFillStyle('rgba(102, 126, 234, 0.5)')
  ctx.setFontSize(12)
  ctx.setTextAlign('center')
  ctx.setTextBaseline('middle')
  ctx.fillText('数据加载中...', chartConfig.width / 2, chartConfig.height / 2)
}

// 绘制关键节点 - 空心圆样式，添加安全检查
function drawKeyPoints(ctx: any) {
  // 🚀 优化：如果没有数据，绘制占位节点
  if (!props.fortuneData?.decadeFortunes || props.fortuneData.decadeFortunes.length === 0) {
    drawPlaceholderPoints(ctx)
    return
  }

  const decadeCount = props.fortuneData.decadeFortunes.length

  props.fortuneData.decadeFortunes.forEach((decade, index) => {
    const x = chartConfig.paddingLeft + (index / Math.max(decadeCount - 1, 1)) * chartInner.value.width
    const y = chartConfig.paddingTop + chartInner.value.height - (decade.score / 100) * chartInner.value.height

    // 绘制空心圆 - 白色填充 + 蓝色边框
    ctx.setFillStyle('rgba(255, 255, 255, 0.9)')
    ctx.setStrokeStyle('#667eea')
    ctx.setLineWidth(2)

    ctx.beginPath()
    ctx.arc(x, y, 4, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
  })
}

// 🚀 新增：绘制占位节点
function drawPlaceholderPoints(ctx: any) {
  // 绘制几个占位节点
  const points = [
    { x: chartConfig.paddingLeft, y: chartConfig.paddingTop + chartInner.value.height * 0.7 },
    { x: chartConfig.paddingLeft + chartInner.value.width * 0.3, y: chartConfig.paddingTop + chartInner.value.height * 0.5 },
    { x: chartConfig.paddingLeft + chartInner.value.width * 0.7, y: chartConfig.paddingTop + chartInner.value.height * 0.6 },
    { x: chartConfig.paddingLeft + chartInner.value.width, y: chartConfig.paddingTop + chartInner.value.height * 0.4 },
  ]

  points.forEach((point) => {
    // 绘制半透明的占位圆点
    ctx.setFillStyle('rgba(255, 255, 255, 0.5)')
    ctx.setStrokeStyle('rgba(102, 126, 234, 0.3)')
    ctx.setLineWidth(1)

    ctx.beginPath()
    ctx.arc(point.x, point.y, 3, 0, 2 * Math.PI)
    ctx.fill()
    ctx.stroke()
  })
}

// 防抖计时器
let debounceTimer: number | null = null

// 监听属性变化重新绘制 - 强化防抖机制避免iOS重绘闪动
watch([() => props.fortuneData], (newValues, oldValues) => {
  const [newData] = newValues
  const [oldData] = oldValues || []

  // 检查是否为数据首次加载
  const isFirstDataLoad = (!oldData || !oldData.decadeFortunes || oldData.decadeFortunes.length === 0)
    && (newData && newData.decadeFortunes && newData.decadeFortunes.length > 0)

  if (isFirstDataLoad) {
    console.warn('OverallFortune: 检测到数据首次加载，使用智能延时绘制')
    smartDelayedDraw()
    return
  }
  // 如果正在绘制，则跳过重绘
  if (isDrawing.value)
    return

  // 清除之前的计时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  // 设置新的防抖计时器，进一步增加延迟
  debounceTimer = setTimeout(() => {
    nextTick(() => {
      drawChart()
    })
    debounceTimer = null
  }, 600) // 🚀 增加到600ms防抖延迟，减少iOS重绘频率和滚动抖动
}, { deep: true })

// 数据就绪检查函数 - 放宽检查条件，允许空数据时也显示占位图表
function checkDataReady(): boolean {
  // 🚀 优化：只要有fortuneData对象就认为数据就绪，即使decadeFortunes为空也可以显示占位图表
  const isReady = !!props.fortuneData

  console.warn('OverallFortune数据检查:', {
    hasFortuneData: !!props.fortuneData,
    hasDecadeFortunes: !!props.fortuneData?.decadeFortunes,
    isDecadeFortunesArray: Array.isArray(props.fortuneData?.decadeFortunes),
    decadeFortunesLength: props.fortuneData?.decadeFortunes?.length || 0,
    currentDecade: props.fortuneData?.currentDecade,
    isReady,
    // 详细的数据结构检查
    fortuneDataKeys: props.fortuneData ? Object.keys(props.fortuneData) : [],
    decadeFortunesData: props.fortuneData?.decadeFortunes?.slice(0, 2), // 只显示前2个用于调试
  })

  return isReady
}

// iOS设备检测
function isIOSDevice(): boolean {
  const deviceInfo = uni.getDeviceInfo()
  return deviceInfo.platform === 'ios'
}

// 智能延时绘制
function smartDelayedDraw() {
  console.warn('OverallFortune: 开始智能延时绘制检查', {
    canvasId,
    chartConfig,
    hasFortuneData: !!props.fortuneData,
    decadeFortunesLength: props.fortuneData?.decadeFortunes?.length || 0,
    isScrolling: isScrolling.value,
    isPageScrolling: isPageScrolling.value,
  })

  // 检查数据状态（但不阻止绘制）
  const dataReady = checkDataReady()
  console.warn('OverallFortune: 数据检查结果:', dataReady)

  // 🚀 重置滚动状态，确保初始绘制不被阻止
  isScrolling.value = false

  // iOS设备使用更长延时，确保渲染完成
  const delay = isIOSDevice() ? 1000 : 500 // 增加延时确保容器渲染完成

  setTimeout(() => {
    console.warn('OverallFortune: 延时绘制开始', {
      delay: `${delay}ms`,
      canvasReady: canvasReady.value,
      isDrawing: isDrawing.value,
      isScrolling: isScrolling.value,
      isPageScrolling: isPageScrolling.value,
    })
    drawChart()
  }, delay)
}

// 组件挂载时初始化
onMounted(() => {
  nextTick(() => {
    smartDelayedDraw()
  })
})
</script>

<template>
  <view class="overall-fortune-card">
    <view class="card-header">
      <text class="card-title">
        整体运势
      </text>
    </view>

    <!-- 运势趋势图 -->
    <view class="chart-container">
      <view class="chart-title">
        <text class="title-text">
          一生运势趋势图
        </text>
      </view>

      <scroll-view
        ref="scrollContainer"
        class="chart-scroll"
        scroll-x
        :show-scrollbar="false"
        @scroll="handleScroll"
      >
        <view class="chart-wrapper">
          <!-- Canvas图形 - 🚀 新方案：使用transform和opacity优化滚动性能 -->
          <canvas
            :id="canvasId"
            :canvas-id="canvasId"
            class="chart-canvas"
            :class="{ 'canvas-scrolling': isScrolling || isPageScrolling }"
            :force-use-old-canvas="true"
            :style="{
              width: `${chartConfig.width}px`,
              height: `${chartConfig.height}px`,
            }"
            @error="handleCanvasError"
          />
        </view>
      </scroll-view>
    </view>

    <!-- 当前十年详情 -->
    <view class="current-decade-detail">
      <view class="detail-header">
        <text class="detail-title">
          第{{ currentDecadeData.decade }}个十年
        </text>
        <text class="detail-subtitle">
          {{ currentDecadeData.yearRange }}
        </text>
      </view>

      <view class="detail-content">
        <view class="score-theme-section">
          <view class="theme-section">
            <text class="theme-label">
              主题：
            </text>
            <text class="theme-text">
              {{ currentDecadeData.theme }}
            </text>
          </view>

          <view class="score-section">
            <view class="score-display">
              <text class="score-value">
                {{ currentDecadeData.score }}
              </text>
              <text class="score-unit">
                分
              </text>
            </view>
          </view>
        </view>

        <view class="description-section">
          <text class="description-text">
            {{ currentDecadeData.description }}
          </text>
        </view>

        <view v-if="currentDecadeData.keyEvents.length > 0" class="events-section">
          <text class="events-label">
            关键事件：
          </text>
          <view class="events-list">
            <text
              v-for="event in currentDecadeData.keyEvents"
              :key="event"
              class="event-item"
            >
              • {{ event }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 人生概览 - 恢复背景和标题，添加安全检查 -->
    <view v-if="props.fortuneData?.lifetimeOverview" class="lifetime-overview">
      <view class="overview-header">
        <text class="iconfont icon-yunshi overview-icon" />
        <text class="overview-title">
          人生运势概览
        </text>
      </view>
      <text class="overview-description">
        {{ props.fortuneData.lifetimeOverview }}
      </text>
    </view>

    <!-- 🚀 数据加载状态提示 -->
    <view v-else-if="!props.fortuneData" class="data-loading">
      <text class="loading-text">
        数据加载中...
      </text>
    </view>
    <view v-else class="data-placeholder">
      <text class="placeholder-text">
        暂无运势概览数据
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.overall-fortune-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 24px;
  padding: 24px;
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary, #333333);
}

.chart-container {
  margin-bottom: 8px; /* 减少底部间距 */

  // � 关键：设置明确的高度，确保Canvas容器有足够空间显示
  min-height: 210px; /* 减少容器高度，紧凑布局 */

  // �🚀 Canvas滚动性能优化
  will-change: transform;
  contain: layout style paint;
  transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);

  // 🚀 防止滚动时的层级抖动
  isolation: isolate;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.chart-title {
  margin-bottom: 12px;
  text-align: center;
}

.title-text {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-primary, #333333);
}

.chart-scroll {
  width: 100%;
  height: 200px; /* 减少高度，与Canvas高度一致 */
  white-space: nowrap;
  overflow: hidden;

  // Canvas滚动性能优化
  -webkit-overflow-scrolling: touch;
  will-change: transform;
  contain: strict; /* 最严格的包含，防止影响其他元素 */
  transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);

  // 防止滚动时的层级抖动
  isolation: isolate;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
}

.chart-wrapper {
  display: inline-block;
  min-width: 100%;
  height: 200px; /* 关键：设置明确的高度，与Canvas高度一致 */

  // Canvas容器性能优化
  contain: layout style paint;
  transform: translateZ(0);
}

.chart-canvas {
  display: block;
  margin: 0 auto;

  // Canvas滚动性能优化
  will-change: transform; /* 明确告知会发生变换 */
  transform: translate3d(0, 0, 0); /* 强制GPU加速 */
  -webkit-transform: translate3d(0, 0, 0);
  contain: strict; /* 最严格的包含，防止影响其他元素 */

  // 🚀 关键优化：滚动时固定定位，解决抖动问题
  transition: none; /* 禁用过渡动画，减少抖动 */
}

// 🚀 滚动时应用的优化样式
.canvas-scrolling {
  // 🚀 关键：滚动时轻微缩放，强制使用合成层
  transform: translate3d(0, 0, 0) scale(0.999) !important;
  -webkit-transform: translate3d(0, 0, 0) scale(0.999) !important;

  // 🚀 滚动时降低透明度，减少视觉冲击
  opacity: 0.95;

  // 🚀 滚动时禁用指针事件，减少交互干扰
  pointer-events: none;

  // 🚀 强制硬件加速
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;

  // 🚀 优化渲染
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.current-decade-detail {
  margin-bottom: 20px;
}

.detail-header {
  margin-bottom: 12px;
}

.detail-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--color-text-primary, #333333);
}

.detail-subtitle {
  font-size: 10px;
  color: var(--color-text-secondary, #666666);
}

.detail-content {
  padding: 16px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.score-theme-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.theme-section {
  flex: 1;
  max-width: 60%;
}

.theme-label {
  font-size: 10px;
  font-weight: 500;
  color: var(--color-text-secondary, #666666);
}

.theme-text {
  font-size: 12px;
  font-weight: 600;
  color: var(--color-text-primary, #333333);
}

.score-section {
  flex-shrink: 0;
  text-align: right;
}

.score-display {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.score-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--color-primary, #667eea);
}

.score-unit {
  font-size: 10px;
  color: var(--color-text-secondary, #666666);
}

.description-section {
  margin-bottom: 4px;
}

.description-text {
  font-size: 10px;
  line-height: 1.5;
  color: var(--color-text-secondary, #666666);
}

.events-section {
  margin-top: 8px;
}

.events-label {
  font-size: 10px;
  font-weight: 500;
  color: var(--color-text-secondary, #666666);
  margin-bottom: 4px;
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-left: 8px;
}

.event-item {
  font-size: 10px;
  line-height: 1.4;
  color: var(--color-text-secondary, #666666);
}

.lifetime-overview {
  padding: 16px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  margin-top: 8px;
}

.overview-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.overview-icon {
  font-size: 16px;
  color: var(--color-primary, #667eea);
}

.overview-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--color-text-primary, #333333);
}

.overview-description {
  font-size: 10px;
  line-height: 1.5;
  color: var(--color-text-secondary, #666666);
}

// 🚀 数据状态样式
.data-loading,
.data-placeholder {
  padding: 16px;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  margin-top: 8px;
}

.loading-text,
.placeholder-text {
  font-size: 10px;
  color: var(--color-text-secondary, #666666);
  opacity: 0.7;
}
</style>
